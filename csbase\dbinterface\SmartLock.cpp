#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/SmartLock.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AkcsCommonDef.h"

namespace dbinterface {

static const std::string smart_lock_info_sec = " UUID,Name,MAC,WifiStatus,KeepAlive,SecretKey,LastConnectedTime,DeviceUUID,Relay,\
    AutoLockEnable,AutoLockDelay,PinCode,IsPinCodeSynchronizing,BatteryLevel,ModuleVersion,LockBodyVersion,InstallerUUID,\
    ProjectType,AccountUUID,CommunityUnitUUID,PersonalAccountUUID,MqttPwd,CombinedVersion,Model ";

void SmartLock::GetSmartLockFromSql(SmartLockInfo& smart_lock_info, CRldbQuery& query)
{
    Snprintf(smart_lock_info.uuid, sizeof(smart_lock_info.uuid), query.GetRowData(0));
    Snprintf(smart_lock_info.name, sizeof(smart_lock_info.name), query.GetRowData(1));
    Snprintf(smart_lock_info.mac, sizeof(smart_lock_info.mac), query.GetRowData(2));
    smart_lock_info.wifi_status = ATOI(query.GetRowData(3));
    smart_lock_info.keep_alive = ATOI(query.GetRowData(4));
    Snprintf(smart_lock_info.secret_key, sizeof(smart_lock_info.secret_key), query.GetRowData(5));
    Snprintf(smart_lock_info.last_connected_time, sizeof(smart_lock_info.last_connected_time), query.GetRowData(6));
    Snprintf(smart_lock_info.device_uuid, sizeof(smart_lock_info.device_uuid), query.GetRowData(7));
    smart_lock_info.relay = ATOI(query.GetRowData(8));
    smart_lock_info.auto_lock_enable = ATOI(query.GetRowData(9));
    smart_lock_info.auto_lock_delay = ATOI(query.GetRowData(10));
    Snprintf(smart_lock_info.pin_code, sizeof(smart_lock_info.pin_code), query.GetRowData(11));
    smart_lock_info.is_pin_code_synchronizing = ATOI(query.GetRowData(12));
    smart_lock_info.battery_level = ATOI(query.GetRowData(13));
    Snprintf(smart_lock_info.module_version, sizeof(smart_lock_info.module_version), query.GetRowData(14));
    Snprintf(smart_lock_info.lock_body_version, sizeof(smart_lock_info.lock_body_version), query.GetRowData(15));
    Snprintf(smart_lock_info.installer_uuid, sizeof(smart_lock_info.installer_uuid), query.GetRowData(16));
    smart_lock_info.project_type = ATOI(query.GetRowData(17));
    Snprintf(smart_lock_info.account_uuid, sizeof(smart_lock_info.account_uuid), query.GetRowData(18));
    Snprintf(smart_lock_info.community_unit_uuid, sizeof(smart_lock_info.community_unit_uuid), query.GetRowData(19));
    Snprintf(smart_lock_info.personal_account_uuid, sizeof(smart_lock_info.personal_account_uuid), query.GetRowData(20));
    Snprintf(smart_lock_info.mqtt_pwd, sizeof(smart_lock_info.mqtt_pwd), query.GetRowData(21));
    Snprintf(smart_lock_info.combined_version, sizeof(smart_lock_info.combined_version), query.GetRowData(22));
    smart_lock_info.model = (SMARTLOCK_MODEL)ATOI(query.GetRowData(23));

    std::string srcpwd = smart_lock_info.mqtt_pwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), smart_lock_info.mqtt_pwd, sizeof(smart_lock_info.mqtt_pwd));
    return;
}

int SmartLock::GetSmartLockInfoByUUID(const std::string& uuid, SmartLockInfo& smart_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_info_sec << " from SmartLock where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockFromSql(smart_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

// 一台设备多个relay,每个relay都可以绑定一把锁
int SmartLock::GetSmartLockListByDeviceUUID(const std::string& device_uuid, SmartLockInfoList& smartlock_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_info_sec <<" from SmartLock where DeviceUUID = '" << device_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SmartLockInfo smartlock_info;
        GetSmartLockFromSql(smartlock_info, query);
        smartlock_info_list.push_back(smartlock_info);
    }
    
    return 0;
}

int SmartLock::GetSmartLockInfoListByPersonalAccountUUID(const std::string& per_uuid, SmartLockInfoList& smartlock_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_info_sec <<" from SmartLock where PersonalAccountUUID = '" << per_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SmartLockInfo smartlock_info;
        GetSmartLockFromSql(smartlock_info, query);
        smartlock_info_list.push_back(smartlock_info);
    }
    
    return 0;
}

int SmartLock::GetSmartLockInfoByMac(const std::string& mac, SmartLockInfo& smart_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_info_sec << " from SmartLock where MAC = '" << mac << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockFromSql(smart_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockInfo by MAC failed, MAC = " << mac;
        return -1;
    }
    return 0;
}


int SmartLock::UpdateSmartLockRelatedInfo(const SmartLockInfo& smart_lock_info,  bool pincode_already_sync)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    int is_pincode_synchronizing = pincode_already_sync ? 0 : 1;

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLock set LastConnectedTime=now(), ModuleVersion='%s', LockBodyVersion='%s', CombinedVersion='%s', IsPinCodeSynchronizing=%d where UUID='%s'",
        smart_lock_info.module_version, smart_lock_info.lock_body_version, smart_lock_info.combined_version, is_pincode_synchronizing, smart_lock_info.uuid
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

int SmartLock::UpdateBatteryLevel(const std::string& lock_uuid, int battery_level)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLock set BatteryLevel=%d where UUID='%s'",
        battery_level, lock_uuid.c_str()
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

void SmartLock::GetSmartLockUUIDListByNode(const std::string& node, std::set<std::string>& smartlock_info_uuid_list)
{
    std::string uuid;
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(node, uuid))
    {
        AK_LOG_WARN << "node info not found. node=" << node;
        return;
    }

    SmartLockInfoList smartlock_info_list;
    if (0 != dbinterface::SmartLock::GetSmartLockInfoListByPersonalAccountUUID(uuid, smartlock_info_list))
    {
        AK_LOG_WARN << "get SmartLockInfoListByPersonalAccountUUID failed. node=" << node;
        return;
    }

    for (const auto& smartlock_info : smartlock_info_list)
    {
        smartlock_info_uuid_list.insert(smartlock_info.uuid);
    }

    return;
}


int SmartLock::GetSmartLockInfoListByAccountUUID(const std::string& account_uuid, SmartLockInfoList& smartlock_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_info_sec << " from SmartLock where AccountUUID = '" << account_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SmartLockInfo smartlock_info;
        GetSmartLockFromSql(smartlock_info, query);
        smartlock_info_list.push_back(smartlock_info);
    }
    
    return 0;
}

void SmartLock::UpdateNodeSL20LockStatusSynchronizing(const std::string& node_uuid)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    // 1: 未同步，0: 已同步
    int is_pincode_synchronizing = 1;

    // 只更新未保活的锁
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLock set IsPinCodeSynchronizing=%d where PersonalAccountUUID='%s' and KeepAlive=0 and Model = %d",
        is_pincode_synchronizing, node_uuid.c_str(), (int)SmartLockModel::SL20
    );

    db_conn->Execute(sql);
}

void SmartLock::UpdateProjectSL20LockStatusSynchronizing(const std::string& project_uuid)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    // 1: 未同步，0: 已同步
    int is_pincode_synchronizing = 1;
    int smartlock_model = (int)SmartLockModel::SL20;

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLock set IsPinCodeSynchronizing=%d where AccountUUID='%s' and KeepAlive=0 and Model = %d",
        is_pincode_synchronizing, project_uuid.c_str(), smartlock_model
    );

    db_conn->Execute(sql);
}

void SmartLock::UpdateOfflineCodeUsed(const std::string& lock_uuid, const std::string& note)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLock set OfflineCodeLastGenerateTime = '0000-00-00 00:00:00' where UUID='%s' and OfflineCode='%s'", lock_uuid.c_str(), note.c_str());
    db_conn->Execute(sql);
}

int SmartLock::UpdateSmartLockInfoByUUID(const std::string& uuid, const SmartLockInfo& smart_lock_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    std::stringstream stream_sql;
    stream_sql << "update SmartLock set WifiStatus=" << smart_lock_info.wifi_status
               << ", LastConnectedTime='" << smart_lock_info.last_connected_time
               << "', LockBodyVersion='" << smart_lock_info.lock_body_version
               << "' where UUID='" << uuid << "'";

    int ret = db_conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;
    if (ret != 0) {
        AK_LOG_WARN << "UpdateSmartLockInfoByUUID failed, UUID = " << uuid;
    }
    return ret;
}

int SmartLock::UpdateSmartLockCombinedVersionByUUID(const std::string& lock_uuid, const std::string& module_version)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    std::stringstream stream_sql;
    stream_sql << "update SmartLock set CombinedVersion = '" << module_version << "' where UUID = '" << lock_uuid << "'";

    return db_conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;
}

}
