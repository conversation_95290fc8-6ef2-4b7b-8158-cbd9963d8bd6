#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SL50Lock.h"
#include "AkcsPasswdConfuse.h"

namespace dbinterface {

static const std::string sl50_lock_info_sec = " UUID,SmartLockUUID,WifiName,LastConnectedTime,Language,Volume,StayAlarm,StayAlarmTime,MonitoringScope,EnableTwoFactoryAuth,SipAccount,SipPwd,RtspPwd ";

void SL50Lock::GetSL50LockFromSql(SL50LockInfo& sl50_lock_info, CRldbQuery& query)
{
    Snprintf(sl50_lock_info.uuid, sizeof(sl50_lock_info.uuid), query.GetRowData(0));
    Snprintf(sl50_lock_info.smartlock_uuid, sizeof(sl50_lock_info.smartlock_uuid), query.GetRowData(1));
    Snprintf(sl50_lock_info.wifi_name, sizeof(sl50_lock_info.wifi_name), query.GetRowData(2));
    Snprintf(sl50_lock_info.last_connected_time, sizeof(sl50_lock_info.last_connected_time), query.GetRowData(3));
    Snprintf(sl50_lock_info.language, sizeof(sl50_lock_info.language), query.GetRowData(4));
    Snprintf(sl50_lock_info.volume, sizeof(sl50_lock_info.volume), query.GetRowData(5));
    sl50_lock_info.stay_alarm = ATOI(query.GetRowData(6));
    sl50_lock_info.stay_alarm_time = ATOI(query.GetRowData(7));
    Snprintf(sl50_lock_info.monitoring_scope, sizeof(sl50_lock_info.monitoring_scope), query.GetRowData(8));
    sl50_lock_info.enable_two_factory_auth = ATOI(query.GetRowData(9));
    Snprintf(sl50_lock_info.sip_account, sizeof(sl50_lock_info.sip_account), query.GetRowData(10));
    Snprintf(sl50_lock_info.sip_pwd, sizeof(sl50_lock_info.sip_pwd), query.GetRowData(11));
    Snprintf(sl50_lock_info.rtsp_pwd, sizeof(sl50_lock_info.rtsp_pwd), query.GetRowData(12));

    std::string srcpwd = sl50_lock_info.rtsp_pwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), sl50_lock_info.rtsp_pwd, sizeof(sl50_lock_info.rtsp_pwd));
    srcpwd = sl50_lock_info.sip_pwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), sl50_lock_info.sip_pwd, sizeof(sl50_lock_info.sip_pwd));    
}

int SL50Lock::GetSL50LockInfoBySmartLockUUID(const std::string& smartlock_uuid, SL50LockInfo& sl50_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl50_lock_info_sec << " from SL50Lock where SmartLockUUID = '" << smartlock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL50LockFromSql(sl50_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL50LockInfo by SmartLockUUID failed, SmartLockUUID = " << smartlock_uuid;
        return -1;
    }
    return 0;
}

int SL50Lock::GetSL50LockInfoByUUID(const std::string& uuid, SL50LockInfo& sl50_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl50_lock_info_sec << " from SL50Lock where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL50LockFromSql(sl50_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL50LockInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int SL50Lock::UpdateSL50LockInfoBySmartLockUUID(const std::string& smartlock_uuid, const SL50LockInfo& sl50_lock_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    std::stringstream stream_sql;
    stream_sql << "update SL50Lock set WifiName='" << sl50_lock_info.wifi_name
               << "', LastConnectedTime = '" << sl50_lock_info.last_connected_time
               << "', Language='" << sl50_lock_info.language
               << "', Volume='" << sl50_lock_info.volume
               << "', StayAlarm=" << sl50_lock_info.stay_alarm
               << ", StayAlarmTime=" << sl50_lock_info.stay_alarm_time
               << ", MonitoringScope='" << sl50_lock_info.monitoring_scope
               << "', EnableTwoFactoryAuth=" << sl50_lock_info.enable_two_factory_auth
               << " where SmartLockUUID='" << smartlock_uuid << "'";

    int ret = db_conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;
    if (ret != 0) {
        AK_LOG_WARN << "UpdateSL50LockInfo failed, SmartLockUUID = " << sl50_lock_info.smartlock_uuid;
    }
    return ret;
}

} // namespace dbinterface
