#!/bin/bash
set -euo pipefail
# 用法: bash gdb_debug_helper.sh 组件名
if [ $# -ne 1 ]; then
  echo "用法: $0 <组件名>"
  echo "示例: bash gdb_debug_helper.sh csadapt"
  exit 1
fi

COMPONENT="$1"

# 1. 获取运行中该组件的镜像名+版本，并获取-v和-e参数
CONTAINER_ID=$(docker ps | awk -v comp="$COMPONENT" '$NF == comp {print $1; exit}')
IMAGE_FULL=$(docker ps | awk -v comp="$COMPONENT" '$NF == comp {print $2; exit}')
if [ -z "$IMAGE_FULL" ] || [ -z "$CONTAINER_ID" ]; then
  echo "未找到正在运行的组件: $COMPONENT"
  exit 2
fi

# 获取-v参数（简化版）
VOLUME_ARGS=$(docker inspect "$CONTAINER_ID" | \
  awk '
    /"Volumes": \{/ {in_vol=1; next}
    in_vol && /\}/ {in_vol=0}
    in_vol && /:/ {
      gsub(/[",]/, "")
      split($0, arr, ":")
      host=arr[2]; cont=arr[1]
      gsub(/^ +| +$/, "", host)
      gsub(/^ +| +$/, "", cont)
      if(length(host)>0 && length(cont)>0) printf("-v %s:%s ", host, cont)
    }
  ')

# 获取-e参数（简化版）
ENV_ARGS=$(docker inspect "$CONTAINER_ID" | \
  awk '
    /"Env": \[/ {in_env=1; next}
    in_env && /\]/ {in_env=0}
    in_env && !/^\s*$/ {
      gsub(/"/, "")
      sub(/,$/, "")
      gsub(/^\s+|\s+$/, "")
      if(length($0)>0) printf("-e %s ", $0)
    }
  ')

# 2. 拼接_debug
DEBUG_IMAGE="${IMAGE_FULL}_debug"

# 3. 获取debug镜像中的存档文件
mkdir -p /root/cpp_ubuntu20_debug
docker run --rm -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug "$DEBUG_IMAGE" cp -rf /archive /root/cpp_ubuntu20_debug
chmod 777 -R /root/cpp_ubuntu20_debug

# 4. 启动gdb容器（先判断是否已存在）
if docker ps | grep -q gdb_docker_ubuntu20; then
  echo "gdb_docker_ubuntu20 容器已存在"
  docker stop gdb_docker_ubuntu20
  docker rm gdb_docker_ubuntu20
fi


docker run -itd --restart=always --net=host \
  -e TZ=Asia/Shanghai \
  -v /root/cpp_ubuntu20_debug/archive:/usr/local/akcs \
  -v /var/core:/var/core \
  $VOLUME_ARGS $ENV_ARGS \
  --name gdb_docker_ubuntu20 \
  registry.cn-hangzhou.aliyuncs.com/ak_system/gdb_docker_ubuntu20:1.0 \
  /bin/bash -c "/usr/local/akcs/${COMPONENT}/scripts/sedconf.sh; exec /bin/bash"


# 5. rmi掉临时debug镜像
docker rmi "$DEBUG_IMAGE"

# 6. 提示后续操作
echo "--------------------------------------------------------------------------------------"
echo "接下来你可以执行以下命令进入gdb容器进行调试："
echo "docker exec -it gdb_docker_ubuntu20 /bin/bash"
echo "调试完毕后记得exit退出容器哦"
