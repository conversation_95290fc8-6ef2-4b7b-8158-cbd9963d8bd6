#include "WebSocketClient.h"
#include "AkLogging.h"
#include "util_time.h"
#include "util_string.h"
#include "json/json.h"
#include "SHA1.h"
#include "Base64.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include "YunJiRobotConfig.h"

extern YUNJI_ROBOT_CONFIG g_yunji_robot_config;

// 构造函数
YunJiRobotWebSocketClient::YunJiRobotWebSocketClient(const std::string& wss_url, const std::string& access_key_id, const std::string& access_key_secret) 
    : wss_url_(wss_url), access_key_id_(access_key_id), access_key_secret_(access_key_secret), is_connected_(false), enable_heartbeat_(true) {
    AuthenticatedUrl();
}

// 析构函数
YunJiRobotWebSocketClient::~YunJiRobotWebSocketClient() {
    Disconnect();
}

// 生成认证URL
void YunJiRobotWebSocketClient::AuthenticatedUrl() {
    // 签名随机数
    std::string signature_nonce = GetNbitRandomString(36);

    // 获取时间戳
    std::string timestamp = GetISO8601ChinaTimestampString();        

    // 计算signature
    std::string signature_key = access_key_secret_ + std::string("&");
    std::string to_sign = "accessKeyId=" + access_key_id_ + "&signatureNonce=" + signature_nonce + "&timestamp=" + timestamp;
    std::string signature = GetBase64Str(HMACSHA1::compute_bin(signature_key, to_sign));

    // 认证参数
    std::string auth_params = "accessKeyId=" + access_key_id_ + "&timestamp=" + timestamp + "&signatureNonce=" + signature_nonce + "&signature=" + signature;

    AK_LOG_INFO << "signature_nonce = " << signature_nonce;
    AK_LOG_INFO << "timestamp = " << timestamp;
    AK_LOG_INFO << "signature_key = " << signature_key;
    AK_LOG_INFO << "to_sign = " << to_sign;
    AK_LOG_INFO << "signature = " << signature;
    AK_LOG_INFO << "auth_params = " << auth_params;
    AK_LOG_INFO << "access_key_id_ = " << access_key_id_;
    AK_LOG_INFO << "access_key_secret_ = " << access_key_secret_;

    // 构造完整的WebSocket URL with 认证参数
    authenticated_url_ = wss_url_ + std::string("?") + "accessKeyId=" + access_key_id_ 
                + "&timestamp=" + URLEncode(timestamp) + "&signatureNonce=" + signature_nonce + "&signature=" + URLEncode(signature);
    
    AK_LOG_INFO << "authenticated_url_: " << authenticated_url_;
}

// 连接WebSocket
void YunJiRobotWebSocketClient::Connect() {
    AK_LOG_INFO << "开始WebSocket连接...";
    AK_LOG_INFO << "目标URL: " << authenticated_url_;
    
    // 临时使用固定的成功URL进行测试
    std::string test_url = "wss://bj-open-api.yunjiai.cn/v3/wsapi?accessKeyId=bg7WG9q6bFBls1fn&timestamp=2025-07-06T13%3A14%3A00%2B08%3A00&signatureNonce=53c593e7-766d-4646-8b58-0b795ded0ed6&signature=kaYRADCjZjn9ThQMCODcerkAJc0%3D";

    AK_LOG_INFO << "使用测试URL替代动态URL";

    // 设置WebSocket URL (使用测试URL)
    web_socket_.setUrl(test_url);

    // 设置请求头
    ix::WebSocketHttpHeaders headers;
    headers["User-Agent"] = "YunJiRobotWebSocketClient/1.0";

    web_socket_.setExtraHeaders(headers);

    // 设置消息处理回调
    web_socket_.setOnMessageCallback([this](const ix::WebSocketMessagePtr& msg) {
        HandleMessage(msg);
    });

    // 开始连接
    AK_LOG_INFO << "启动WebSocket连接...";
    web_socket_.start();

    // 添加连接超时检测
    AK_LOG_INFO << "WebSocket连接已启动，等待连接结果...";
}

// 断开连接
void YunJiRobotWebSocketClient::Disconnect() {
    AK_LOG_INFO << "正在断开连接...";
    enable_heartbeat_ = false;
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    web_socket_.stop();
    is_connected_ = false;
}

// 发送文本消息
void YunJiRobotWebSocketClient::SendTextMessage(const std::string& message) 
{
    if (!is_connected_) {
        AK_LOG_INFO << "错误: WebSocket未连接";
        return;
    }
    
    AK_LOG_INFO << "发送消息: " << message;
    web_socket_.send(message);
}

// 发送JSON格式的请求
void YunJiRobotWebSocketClient::SendRequest(const std::string& action, const std::vector<std::string>& devices, const std::string& cid)
{
    Json::Value request_json;   
    Json::FastWriter writer;

    // 根据不同的 action 构建不同格式的消息
    if (action == "subscribe_robot_position" ||
        action == "subscribe_device_status" ||
        action == "subscribe_device_task_events") {

        // 构建订阅消息格式
        request_json["cmd"] = action;
        // 使用传入的 cid，如果为空则自动生成
        request_json["cid"] = cid.empty() ? GetNbitRandomString(36) : cid;
        request_json["timestamp"] = GetCurrentMilliTimeStamp();

        // 构建设备列表
        Json::Value data_json;
        Json::Value devices_array(Json::arrayValue);

        // 将 vector 中的设备ID添加到 JSON 数组
        for (const auto& device_id : devices) {
            devices_array.append(device_id);
        }

        data_json["devices"] = devices_array;
        request_json["data"] = data_json;

    } else {
        // 默认格式（兼容原有的消息格式）
        // 对于非订阅消息，将第一个设备ID作为 data（向后兼容）
        request_json["action"] = action;
        request_json["data"] = devices.empty() ? "" : devices[0];
        request_json["timestamp"] = GetCurrentMilliTimeStamp();
    }

    std::string message = writer.write(request_json);
    // 移除 FastWriter 添加的换行符
    if (!message.empty() && message.back() == '\n') {
        message.pop_back();
    }

    SendTextMessage(message);
}

// 订阅设备任务事件
void YunJiRobotWebSocketClient::SubscribeDeviceTaskEvents(const std::vector<std::string>& devices, const std::string& cid) {
    SendRequest("subscribe_device_task_events", devices, cid);

    // 构建设备列表字符串用于日志
    std::string devices_str = "[";
    for (size_t i = 0; i < devices.size(); ++i) {
        devices_str += "\"" + devices[i] + "\"";
        if (i < devices.size() - 1) devices_str += ", ";
    }
    devices_str += "]";

    AK_LOG_INFO << "发送设备任务事件订阅，设备: " << devices_str << ", CID: " << (cid.empty() ? "auto-generated" : cid);
}

// 发送心跳
void YunJiRobotWebSocketClient::SendPing() 
{
    if (is_connected_) {
        Json::Value ping_json;
        Json::FastWriter writer;

        ping_json["cmd"] = "ping";
        ping_json["timestamp"] = GetCurrentMilliTimeStamp();

        std::string ping_message = writer.write(ping_json);
        // 移除 FastWriter 添加的换行符
        if (!ping_message.empty() && ping_message.back() == '\n') {
            ping_message.pop_back();
        }

        web_socket_.send(ping_message);
        AK_LOG_INFO << "发送心跳: " << ping_message;
    }
}

// 启动心跳线程
void YunJiRobotWebSocketClient::StartHeartbeat(int intervalSeconds) {
    heartbeat_thread_ = std::thread([this, intervalSeconds]() {
        while (enable_heartbeat_) {
            std::this_thread::sleep_for(std::chrono::seconds(intervalSeconds));
            if (is_connected_) {
                SendPing();
            }
        }
    });
}

// 获取连接状态
bool YunJiRobotWebSocketClient::GetIsConnected() const {
    return is_connected_;
}

// 处理WebSocket消息
void YunJiRobotWebSocketClient::HandleMessage(const ix::WebSocketMessagePtr& msg) {
    int msg_type = static_cast<int>(msg->type);
    AK_LOG_INFO << "消息类型: " << msg_type << " (0=Open, 1=Close, 2=Message, 3=Error, 4=Ping, 5=Pong, 6=Fragment)";
    AK_LOG_INFO << "收到消息: " << msg->str;

    switch (msg->type) {
        case ix::WebSocketMessageType::Open:
            OnConnected();
            break;
            
        case ix::WebSocketMessageType::Close:
            is_connected_ = false;
            OnDisconnected();
            break;
            
        case ix::WebSocketMessageType::Error:
            AK_LOG_ERROR << "WebSocket连接错误详情:";
            AK_LOG_ERROR << "  错误原因: " << msg->errorInfo.reason;
            AK_LOG_ERROR << "  HTTP状态码: " << msg->errorInfo.http_status;
            AK_LOG_ERROR << "  重试次数: " << msg->errorInfo.retries;
            AK_LOG_ERROR << "  等待时间: " << msg->errorInfo.wait_time << "ms";
            OnDisconnected();
            break;
            
        case ix::WebSocketMessageType::Message:
            ProcessMessage(msg->str);
            break;
            
        case ix::WebSocketMessageType::Ping:
            AK_LOG_INFO << "收到Ping";
            // WebSocket库会自动回复Pong
            break;
            
        case ix::WebSocketMessageType::Pong:
            AK_LOG_INFO << "收到Pong";
            break;
            
        case ix::WebSocketMessageType::Fragment:
            AK_LOG_INFO << "收到消息片段";
            break;
    }
}

// 处理接收到的消息
void YunJiRobotWebSocketClient::ProcessMessage(const std::string& message) 
{
    AK_LOG_INFO << "收到消息: " << message;
    
    Json::Reader reader;
    Json::Value root;
    
    if (reader.parse(message, root)) {
        std::string cmd = root.get("cmd", "").asString();
        
        if (cmd == "pong") {
            AK_LOG_INFO << "收到心跳响应";
        } else if (cmd == "result") {
            HandleRobotTaskResponse(message);
        } 
    }
}

// 处理聊天响应
void YunJiRobotWebSocketClient::HandleRobotTaskResponse(const std::string& response) 
{
    // 处理流式响应，可能需要拼接内容
    AK_LOG_INFO << "AI响应: " << response;
}

// 连接成功回调
void YunJiRobotWebSocketClient::OnConnected()
{
    AK_LOG_INFO << "WebSocket连接已建立";
    is_connected_ = true;

    // 启动心跳线程
    enable_heartbeat_ = true;
    StartHeartbeat(g_yunji_robot_config.websocket_heartbeat_interval);
}

// 连接断开回调
void YunJiRobotWebSocketClient::OnDisconnected() {
    AK_LOG_INFO << "连接已断开";
    is_connected_ = false;
}

// 订阅机器人位置信息
void YunJiRobotWebSocketClient::SubscribeRobotPosition(const std::vector<std::string>& devices, const std::string& cid) 
{
    SendRequest("subscribe_robot_position", devices, cid);
}

// 订阅设备状态信息
void YunJiRobotWebSocketClient::SubscribeDeviceStatus(const std::vector<std::string>& devices, const std::string& cid) 
{
    SendRequest("subscribe_device_status", devices, cid);
}